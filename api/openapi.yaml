openapi: 3.0.0
info:
  title: ARC Materials Discovery Platform API
  version: 1.0.0
  description: API for autonomous materials discovery and manufacturing
  contact:
    name: ARC Platform Team
    email: <EMAIL>
servers:
  - url: https://api.arc-materials.io/v1
    description: Production server
  - url: https://staging-api.arc-materials.io/v1
    description: Staging server

security:
  - bearerAuth: []

paths:
  /objectives:
    post:
      summary: Submit new research objective
      tags:
        - Objectives
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ObjectiveSpec'
      responses:
        201:
          description: Objective created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    example: "OBJ-2025-000134"
                  status:
                    type: string
                    enum: [created, planning, executing, completed]
                  createdAt:
                    type: string
                    format: date-time
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
    
    get:
      summary: List research objectives
      tags:
        - Objectives
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [created, planning, executing, completed, failed]
        - name: domain
          in: query
          schema:
            type: string
            enum: [magnetics, electrolytes, ferromagnets, custom]
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        200:
          description: List of objectives
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Objective'
                  total:
                    type: integer
                  limit:
                    type: integer
                  offset:
                    type: integer

  /objectives/{objectiveId}:
    get:
      summary: Get objective details
      tags:
        - Objectives
      parameters:
        - name: objectiveId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Objective details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Objective'
        404:
          $ref: '#/components/responses/NotFound'

  /plans/{objectiveId}:
    get:
      summary: Get execution plan for objective
      tags:
        - Planning
      parameters:
        - name: objectiveId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Execution plan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExecutionPlan'
        404:
          $ref: '#/components/responses/NotFound'

  /executions:
    post:
      summary: Start plan execution
      tags:
        - Execution
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - planId
              properties:
                planId:
                  type: string
                parameters:
                  type: object
                  description: Override parameters for execution
      responses:
        201:
          description: Execution started
          content:
            application/json:
              schema:
                type: object
                properties:
                  executionId:
                    type: string
                  status:
                    type: string
                    enum: [started, running, paused, completed, failed]

  /executions/{executionId}:
    get:
      summary: Get execution status
      tags:
        - Execution
      parameters:
        - name: executionId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Execution details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Execution'

  /measurements:
    post:
      summary: Submit measurement result
      tags:
        - Measurements
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MeasurementRecord'
      responses:
        201:
          description: Measurement recorded
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  timestamp:
                    type: string
                    format: date-time

  /samples/{sampleId}/measurements:
    get:
      summary: Get measurements for a sample
      tags:
        - Measurements
      parameters:
        - name: sampleId
          in: path
          required: true
          schema:
            type: string
        - name: property
          in: query
          schema:
            type: string
            description: Filter by property type
      responses:
        200:
          description: List of measurements
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MeasurementRecord'

  /registry/agents:
    get:
      summary: List registered agents
      tags:
        - Registry
      responses:
        200:
          description: List of agents
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AgentInfo'
    
    post:
      summary: Register new agent
      tags:
        - Registry
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentRegistration'
      responses:
        201:
          description: Agent registered

  /health:
    get:
      summary: Health check endpoint
      tags:
        - System
      security: []
      responses:
        200:
          description: System healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [healthy, degraded, unhealthy]
                  version:
                    type: string
                  uptime:
                    type: integer
                    description: Uptime in seconds
                  components:
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        status:
                          type: string
                        message:
                          type: string

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ObjectiveSpec:
      type: object
      required:
        - domain
        - target_metrics
      properties:
        domain:
          type: string
          enum: [magnetics, electrolytes, ferromagnets, custom]
        target_metrics:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/Metric'
        constraints:
          type: object
          properties:
            HRE_free:
              type: boolean
            max_cost_$kg:
              type: number
            temperature_range_C:
              type: object
              properties:
                min:
                  type: number
                max:
                  type: number
        risk_class:
          type: string
          enum: [R1, R2, R3]
          default: R1
    
    Objective:
      allOf:
        - $ref: '#/components/schemas/ObjectiveSpec'
        - type: object
          properties:
            id:
              type: string
            status:
              type: string
              enum: [created, planning, executing, completed, failed]
            createdAt:
              type: string
              format: date-time
            updatedAt:
              type: string
              format: date-time
            experiments:
              type: array
              items:
                type: string
                description: Experiment IDs
    
    Metric:
      type: object
      required:
        - name
        - unit
        - goal
        - value
      properties:
        name:
          type: string
          example: "BH_max"
        unit:
          type: string
          example: "kJ/m^3"
        goal:
          type: string
          enum: [">=", "<=", "==", "approx"]
        value:
          type: number
          example: 300
    
    ExecutionPlan:
      type: object
      properties:
        id:
          type: string
        objectiveId:
          type: string
        steps:
          type: array
          items:
            $ref: '#/components/schemas/PlanStep'
        estimatedDuration:
          type: number
          description: Estimated duration in seconds
        estimatedCost:
          type: number
          description: Estimated cost in USD
    
    PlanStep:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
          enum: [design, simulate, print, sinter, measure, analyze]
        dependencies:
          type: array
          items:
            type: string
            description: Step IDs that must complete first
        agent:
          type: string
          description: Agent responsible for this step
        parameters:
          type: object
        acceptanceCriteria:
          type: array
          items:
            $ref: '#/components/schemas/Metric'
    
    Execution:
      type: object
      properties:
        id:
          type: string
        planId:
          type: string
        status:
          type: string
          enum: [started, running, paused, completed, failed]
        currentStep:
          type: string
        progress:
          type: number
          minimum: 0
          maximum: 100
        startedAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
        results:
          type: array
          items:
            type: object
    
    MeasurementRecord:
      type: object
      required:
        - sample_id
        - property
        - value
        - unit
      properties:
        id:
          type: string
          readOnly: true
        sample_id:
          type: string
        property:
          type: string
          example: "coercivity"
        value:
          type: number
          example: 950.2
        unit:
          type: string
          example: "kA/m"
        uncertainty:
          type: object
          properties:
            type:
              type: string
              enum: [standard, expanded, combined]
            value:
              type: number
            confidence:
              type: number
              minimum: 0
              maximum: 1
        calibration:
          type: object
          properties:
            standard_id:
              type: string
            last_calibrated:
              type: string
              format: date-time
        conditions:
          type: object
          additionalProperties: true
    
    AgentInfo:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
          enum: [planner, design, simulation, printer, postprocess, metrology, critic]
        status:
          type: string
          enum: [online, offline, busy]
        capabilities:
          type: array
          items:
            type: string
        currentLoad:
          type: number
          minimum: 0
          maximum: 1
    
    AgentRegistration:
      type: object
      required:
        - id
        - type
        - capabilities
        - schemas
      properties:
        id:
          type: string
        type:
          type: string
        capabilities:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              version:
                type: string
        schemas:
          type: object
          properties:
            input:
              type: object
              description: JSON Schema for input
            output:
              type: object
              description: JSON Schema for output
        sla:
          type: object
          properties:
            maxLatencyMs:
              type: integer
            availability:
              type: number
        safetyClass:
          type: string
          enum: [S1, S2, S3, R1, R2, R3]

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
              details:
                type: object
    
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Invalid or missing authentication token"
    
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Resource not found"
