apiVersion: apps/v1
kind: Deployment
metadata:
  name: arc-orchestrator
  namespace: arc-materials
spec:
  replicas: 3
  selector:
    matchLabels:
      app: orchestrator
  template:
    metadata:
      labels:
        app: orchestrator
    spec:
      serviceAccountName: arc-orchestrator
      containers:
      - name: orchestrator
        image: arc/orchestrator:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: TRACE_STORE_URL
          valueFrom:
            configMapKeyRef:
              name: arc-config
              key: trace_store_url
        - name: EVENT_BUS_URL
          valueFrom:
            configMapKeyRef:
              name: arc-config
              key: event_bus_url
        - name: DB_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: arc-db-secret
              key: connection_string
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: orchestrator-service
  namespace: arc-materials
spec:
  selector:
    app: orchestrator
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: orchestrator-ingress
  namespace: arc-materials
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.arc-materials.io
    secretName: arc-tls-secret
  rules:
  - host: api.arc-materials.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: orchestrator-service
            port:
              number: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: orchestrator-hpa
  namespace: arc-materials
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: arc-orchestrator
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: arc-config
  namespace: arc-materials
data:
  trace_store_url: "http://jaeger-collector.monitoring:14268/api/traces"
  event_bus_url: "nats://nats.arc-materials:4222"
  log_level: "info"
  max_concurrent_executions: "10"
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: orchestrator-pdb
  namespace: arc-materials
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: orchestrator
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: arc-orchestrator
  namespace: arc-materials
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: arc-orchestrator
  namespace: arc-materials
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["create", "get", "list", "watch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: arc-orchestrator
  namespace: arc-materials
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: arc-orchestrator
subjects:
- kind: ServiceAccount
  name: arc-orchestrator
  namespace: arc-materials
