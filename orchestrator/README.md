# Graph Orchestrator

The Graph Orchestrator is the central execution engine that manages agent workflows using a directed acyclic graph (DAG) pattern.

## Key Features
- State machine execution with recoverable nodes
- Budget & timebox governance (tokens, cost, wall-clock)
- Checkpoint persistence and retry logic
- Event-driven agent coordination

## API Endpoints
- `POST /plan` - Submit objective and receive execution plan
- `POST /execute` - Execute a plan graph
- `GET /trace/{run_id}` - Retrieve execution trace
- `GET /status/{run_id}` - Get current execution status

## Configuration
```yaml
orchestrator:
  max_concurrent_executions: 10
  checkpoint_interval_seconds: 30
  retry_policy:
    max_attempts: 3
    backoff_multiplier: 2
  budget_limits:
    max_tokens_per_execution: 100000
    max_cost_usd: 500
    max_duration_hours: 24
```

## State Management

The orchestrator maintains execution state using a state machine pattern:

```mermaid
stateDiagram-v2
    [*] --> Planning
    Planning --> Executing
    Executing --> Validating
    Validating --> Executing: Retry
    Validating --> Success
    Validating --> Failed
    Executing --> Failed: Error
    Success --> [*]
    Failed --> [*]
```

## Error Handling

### Retry Logic
```typescript
interface RetryPolicy {
  maxAttempts: number;
  backoffMultiplier: number;
  maxBackoffSeconds: number;
  retryableErrors: string[];
}
```

### Compensation Actions
When a node fails after making changes, the orchestrator can execute compensation actions to rollback:

```typescript
interface CompensationAction {
  nodeId: string;
  action: () => Promise<void>;
  order: number;
}
```

## Monitoring

Key metrics exposed:
- `orchestrator_active_executions` - Current number of active executions
- `orchestrator_execution_duration` - Histogram of execution durations
- `orchestrator_node_failures` - Counter of node failures by type
- `orchestrator_budget_exceeded` - Counter of budget limit violations
