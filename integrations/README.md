# Integration Specifications

## Desktop Metal Live Suite Integration

### Authentication
```typescript
interface LiveSuiteAuth {
  endpoint: string;
  apiKey: string;
  refreshToken?: string;
}
```

### Print Job Submission
```typescript
interface PrintJobRequest {
  printer: 'Shop' | 'P1' | 'X25Pro' | 'X160Pro';
  buildFile: string; // S3 URL
  powder: string;
  parameters: {
    layerThickness: number;
    binderType: string;
    saturation: number;
  };
  traceability: {
    powderBatch: string;
    experimentId: string;
  };
}

interface PrintJobResponse {
  jobId: string;
  estimatedDuration: number;
  telemetryStreamUrl: string;
  webhookUrl?: string;
}
```

### Telemetry Stream
```typescript
interface PrinterTelemetry {
  timestamp: string;
  printerId: string;
  jobId: string;
  layer: number;
  temperatures: {
    bed: number;
    chamber: number;
    printhead: number;
  };
  binderFlow: number;
  recoaterSpeed: number;
  alerts: Alert[];
}

interface Alert {
  level: 'info' | 'warning' | 'error';
  code: string;
  message: string;
  timestamp: string;
}
```

### Live Suite API Examples

```bash
# Submit print job
curl -X POST https://api.desktopmetal.com/v1/print-jobs \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "printer": "X25Pro",
    "buildFile": "s3://builds/job-001.build",
    "parameters": {
      "layerThickness": 50,
      "binderType": "TurboFuse",
      "saturation": 85
    }
  }'

# Get job status
curl https://api.desktopmetal.com/v1/print-jobs/${JOB_ID}/status \
  -H "Authorization: Bearer ${API_KEY}"

# Stream telemetry (WebSocket)
wscat -c wss://telemetry.desktopmetal.com/v1/stream/${JOB_ID} \
  -H "Authorization: Bearer ${API_KEY}"
```

## Live Sinter Integration

### Simulation Request
```typescript
interface SinterSimulation {
  geometry: string; // STL file URL
  material: string;
  schedule: SinterSchedule;
  meshDensity: 'coarse' | 'medium' | 'fine';
  supportStructures?: boolean;
}

interface SinterSchedule {
  steps: Array<{
    temperature: number;
    duration: number;
    atmosphere: string;
    rampRate: number;
  }>;
  coolingRate?: number;
}
```

### Compensation Output
```typescript
interface SinterCompensation {
  scalingFactors: {
    x: number;
    y: number;
    z: number;
  };
  distortionMap: number[][][]; // 3D grid of displacement vectors
  predictedDensity: number;
  predictedGrainSize?: number;
  warnings: string[];
  confidence: number;
}
```

### Integration Flow

```mermaid
sequenceDiagram
    participant Agent
    participant LiveSinter
    participant Storage
    
    Agent->>Storage: Upload STL geometry
    Storage-->>Agent: File URL
    
    Agent->>LiveSinter: Submit simulation request
    LiveSinter->>LiveSinter: Run FEA simulation
    LiveSinter-->>Agent: Job ID
    
    loop Check status
        Agent->>LiveSinter: GET /jobs/{id}/status
        LiveSinter-->>Agent: Status update
    end
    
    LiveSinter-->>Agent: Simulation complete
    Agent->>LiveSinter: GET /jobs/{id}/results
    LiveSinter-->>Agent: Compensation data
    
    Agent->>Agent: Apply compensation to geometry
    Agent->>Storage: Save compensated STL
```

## ROS Integration (Future Robotics)

### Message Types
```python
# arc_msgs/MaterialSample.msg
Header header
string sample_id
string experiment_id
geometry_msgs/Pose location
float32 weight
string status  # created, printed, sintered, measured

# arc_msgs/PickAndPlace.msg
string sample_id
geometry_msgs/Pose source
geometry_msgs/Pose destination
string gripper_type
float32 max_force

# arc_msgs/DepowderRequest.msg
string sample_id
float32 duration
string method  # vibration, compressed_air, brush
```

### Service Definitions
```python
# arc_srvs/TransferSample.srv
string sample_id
string source_station
string destination_station
---
bool success
string error_message
float32 duration

# arc_srvs/MeasureProperty.srv
string sample_id
string property  # density, magnetization, conductivity
---
float32 value
float32 uncertainty
string unit
```

## Database Connections

### PostgreSQL Configuration
```yaml
database:
  host: postgres.arc-materials.local
  port: 5432
  database: arc_experiments
  username: ${DB_USER}
  password: ${DB_PASSWORD}
  pool:
    min: 5
    max: 20
    idleTimeoutMillis: 30000
  ssl:
    rejectUnauthorized: true
    ca: /secrets/db-ca.pem
```

### TimescaleDB for Time Series
```sql
-- Create hypertable for telemetry data
CREATE TABLE printer_telemetry (
    time TIMESTAMPTZ NOT NULL,
    printer_id TEXT NOT NULL,
    job_id TEXT NOT NULL,
    metric TEXT NOT NULL,
    value DOUBLE PRECISION NOT NULL
);

SELECT create_hypertable('printer_telemetry', 'time');

-- Create continuous aggregate for hourly stats
CREATE MATERIALIZED VIEW printer_telemetry_hourly
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 hour', time) AS hour,
    printer_id,
    metric,
    avg(value) as avg_value,
    max(value) as max_value,
    min(value) as min_value
FROM printer_telemetry
GROUP BY hour, printer_id, metric;

-- Refresh policy
SELECT add_continuous_aggregate_policy('printer_telemetry_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');
```

### Vector Database (Pinecone/Weaviate)
```typescript
interface VectorDBConfig {
  provider: 'pinecone' | 'weaviate';
  endpoint: string;
  apiKey: string;
  namespace: string;
  dimensions: 1536; // For OpenAI embeddings
}

// Example: Storing material compositions for similarity search
interface MaterialVector {
  id: string;
  vector: number[];
  metadata: {
    composition: Record<string, number>;
    performance: Record<string, number>;
    experimentId: string;
  };
}
```
