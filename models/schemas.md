# Core Data Models

## Objective Specification

```json
{
  "id": "OBJ-2025-000134",
  "domain": "magnetics|electrolytes|ferromagnets|custom",
  "target_metrics": [
    {
      "name": "BH_max",
      "unit": "kJ/m^3",
      "goal": ">=",
      "value": 300
    },
    {
      "name": "Coercivity",
      "unit": "kA/m",
      "goal": ">=",
      "value": 900
    }
  ],
  "risk_class": "R3",
  "constraints": {
    "HRE_free": true,
    "max_cost_$kg": 150
  }
}
```

## Candidate Design

```json
{
  "id": "CD-001",
  "objective_id": "OBJ-2025-000134",
  "composition": {
    "elements": {
      "Fe": 0.76,
      "B": 0.18,
      "C": 0.02,
      "Co": 0.04
    }
  },
  "process_parameters": {
    "printer": "X25Pro",
    "layer_thickness_um": 50,
    "binder": {
      "type": "TurboFuse",
      "saturation_pct": 85
    },
    "sinter_schedule": {
      "profile_id": "SS-001",
      "atmosphere": "H2/N2",
      "hold_steps": [
        {
          "temperature_C": 600,
          "duration_min": 30,
          "ramp_rate_C_min": 10
        },
        {
          "temperature_C": 1100,
          "duration_min": 120,
          "ramp_rate_C_min": 5
        }
      ]
    }
  },
  "evidence": {
    "simulations": [
      {
        "type": "live_sinter",
        "predicted_density": 0.97,
        "predicted_distortion": 0.002
      }
    ],
    "rationales": [
      "Co addition expected to increase Curie temperature",
      "Carbon for grain boundary pinning"
    ]
  },
  "safety": {
    "hazards": ["H2"],
    "mitigations": ["leak-test", "purge", "ventilation"]
  }
}
```

## Print Job Specification

```json
{
  "job_id": "J-001",
  "printer": "X25Pro",
  "build_box_mm": [400, 250, 250],
  "parts": [
    {
      "cad": "s3://arc-designs/part-001.stl",
      "quantity": 6,
      "shrink_compensation": {
        "x": 1.018,
        "y": 1.018,
        "z": 1.022
      }
    }
  ],
  "powder": "316L",
  "layer_thickness_um": 50,
  "binder": "TurboFuse",
  "traceability": {
    "powder_batch": "PB-2025-001",
    "lot": "LOT-001",
    "experiment_id": "EXP-001"
  }
}
```

## Measurement Record

```json
{
  "id": "MEAS-001",
  "sample_id": "SAMP-001",
  "property": "coercivity",
  "value": 950.2,
  "unit": "kA/m",
  "uncertainty": {
    "type": "standard",
    "value": 12.3,
    "confidence": 0.95
  },
  "calibration": {
    "standard_id": "CAL-001",
    "last_calibrated": "2025-01-15T10:30:00Z",
    "certificate": "CERT-2025-001"
  },
  "conditions": {
    "temperature_C": 25,
    "field_strength_T": 2.0,
    "measurement_direction": "parallel"
  },
  "metadata": {
    "instrument": "VSM-001",
    "operator": "metrology-agent",
    "duration_s": 300
  }
}
```

## Agent Decision Record

```json
{
  "id": "DEC-001",
  "experiment_id": "EXP-001",
  "agent_type": "design",
  "decision_type": "composition_selection",
  "inputs": {
    "objective": "OBJ-2025-000134",
    "previous_results": ["MEAS-000", "MEAS-001"],
    "constraints": {"HRE_free": true}
  },
  "outputs": {
    "candidates": [
      {
        "composition": {"Fe": 0.76, "B": 0.18, "C": 0.02, "Co": 0.04},
        "expected_performance": {"BH_max": 320, "Hc": 920},
        "confidence": 0.72
      }
    ],
    "reasoning": "Increased Co content based on positive correlation observed"
  },
  "confidence": 0.72,
  "reflection": "Co addition showed promise but magnetization dropped slightly. Consider grain boundary engineering next.",
  "tokens_used": 4523
}
```

## Type Definitions (TypeScript)

```typescript
// Enums
type Domain = 'magnetics' | 'electrolytes' | 'ferromagnets' | 'custom';
type GoalOperator = '>=' | '<=' | '==' | 'approx';
type RiskClass = 'R1' | 'R2' | 'R3';
type SafetyClass = 'S1' | 'S2' | 'S3';
type PrinterType = 'Shop' | 'P1' | 'X25Pro' | 'X160Pro';

// Interfaces
interface Metric {
  name: string;
  unit: string;
  goal: GoalOperator;
  value: number;
}

interface Composition {
  elements: Record<string, number>; // Element symbol -> fraction
}

interface ProcessParameters {
  printer: PrinterType;
  layer_thickness_um: number;
  binder: {
    type: string;
    saturation_pct: number;
  };
  sinter_schedule: SinterSchedule;
}

interface SinterSchedule {
  profile_id: string;
  atmosphere: string;
  hold_steps: SinterStep[];
}

interface SinterStep {
  temperature_C: number;
  duration_min: number;
  ramp_rate_C_min: number;
}

interface Uncertainty {
  type: 'standard' | 'expanded' | 'combined';
  value: number;
  confidence?: number;
}
```
