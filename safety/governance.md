# Safety & Governance Framework

## Risk Classifications

### Safety Classes
- **S1**: Standard operations, minimal risk
- **S2**: Elevated risk, requires validation
- **S3**: High risk, requires multiple approvals

### Risk Classes  
- **R1**: Routine operations
- **R2**: New parameters within validated ranges
- **R3**: Novel conditions requiring HITL approval

## Guardrail Implementation

```typescript
interface Guardrail {
  id: string;
  type: 'input' | 'output' | 'process';
  conditions: Condition[];
  actions: Action[];
  override: {
    requiresRole: string[];
    requiresApprovals: number;
  };
}

interface Condition {
  parameter: string;
  operator: 'eq' | 'gt' | 'lt' | 'in' | 'regex';
  value: any;
}

interface Action {
  type: 'block' | 'warn' | 'escalate' | 'audit';
  message: string;
  notifyChannels?: string[];
}
```

## HITL Approval Gates

```yaml
approval_gates:
  new_sinter_schedule:
    risk_class: R3
    required_approvals: 2
    timeout_hours: 4
    escalation_chain:
      - role: process_engineer
      - role: lab_manager
      - role: safety_officer
  
  new_material_composition:
    risk_class: R2
    required_approvals: 1
    auto_approve_if:
      - all_elements_in_whitelist: true
      - total_variation_percent: "<5"
```

## Safety Policies

### Material Safety
```yaml
material_safety:
  prohibited_elements:
    - Hg  # Mercury
    - Cd  # Cadmium
    - Pb  # Lead (above threshold)
  
  restricted_elements:
    - name: Be  # Beryllium
      max_percentage: 2
      requires_approval: true
    - name: As  # Arsenic
      max_percentage: 0.5
      requires_approval: true
  
  atmosphere_safety:
    H2:
      max_concentration: 4  # Below LEL
      requires:
        - leak_detection
        - ventilation
        - emergency_shutoff
    
    CO:
      requires:
        - detection_system
        - ventilation
```

### Process Safety
```yaml
process_safety:
  temperature_limits:
    max_temperature_C:
      Shop: 1400
      P1: 1400
      X25Pro: 1600
      X160Pro: 1600
    
  binder_compatibility:
    TurboFuse:
      compatible_powders: ["316L", "17-4PH", "4140"]
      incompatible: ["Al", "Ti"]
    
  powder_handling:
    reactive_powders:
      - Al
      - Mg
      - Ti
    requires:
      - inert_atmosphere
      - grounding
      - no_water_based_binders
```

## Audit Trail

All safety-relevant actions are logged:

```json
{
  "timestamp": "2025-01-15T10:30:45.123Z",
  "action": "parameter_override",
  "actor": "user-123",
  "details": {
    "parameter": "sinter_temperature",
    "original_value": 1100,
    "new_value": 1250,
    "reason": "Improve densification"
  },
  "approvals": [
    {
      "approver": "engineer-456",
      "timestamp": "2025-01-15T10:35:00.000Z",
      "comment": "Within material specs"
    },
    {
      "approver": "manager-789",
      "timestamp": "2025-01-15T10:40:00.000Z",
      "comment": "Approved for testing"
    }
  ]
}
```

## Emergency Procedures

### Automatic Shutdowns
```typescript
interface EmergencyShutdown {
  trigger: string;
  actions: Array<{
    device: string;
    action: 'stop' | 'pause' | 'safe_mode';
    priority: number;
  }>;
  notifications: Array<{
    channel: string;
    message: string;
    severity: 'critical' | 'high' | 'medium';
  }>;
}
```

### Incident Response
```mermaid
flowchart LR
    A[Incident Detected] --> B{Severity?}
    B -->|Critical| C[Immediate Shutdown]
    B -->|High| D[Pause Operations]
    B -->|Medium| E[Alert & Monitor]
    
    C --> F[Notify Emergency Contacts]
    D --> G[Request Intervention]
    E --> H[Log & Continue]
    
    F --> I[Incident Report]
    G --> I
    H --> I
```

## Compliance

### Data Governance
- All experimental data encrypted at rest and in transit
- Project-based access control
- Audit logs retained for 7 years
- ITAR compliance for restricted materials

### Export Controls
```typescript
interface ExportControl {
  checkComposition(composition: Composition): {
    restricted: boolean;
    reasons: string[];
    requiredLicenses: string[];
  };
  
  checkProcess(parameters: ProcessParameters): {
    restricted: boolean;
    reasons: string[];
  };
}
```

### Quality Management
- ISO 9001 compliance for process documentation
- Calibration schedules for all measurement equipment
- Traceability from raw material to final measurement
- Statistical process control (SPC) for critical parameters

## Training Requirements

### Role-Based Training
```yaml
training_requirements:
  researcher:
    - basic_safety
    - data_governance
    - system_overview
  
  process_engineer:
    - advanced_safety
    - equipment_operation
    - approval_procedures
    - emergency_response
  
  lab_manager:
    - all_modules
    - safety_leadership
    - incident_investigation
```
