# Monitoring Configuration

## Key Metrics

### System Metrics
- Agent response times (p50, p95, p99)
- Token usage per agent per experiment
- Queue depths and processing rates
- Error rates by agent and error type
- Resource utilization (CPU, memory, disk)

### Business Metrics
- Experiments per day
- Time to first viable candidate
- Material discovery success rate
- Resource utilization (printer hours, consumables)
- Cost per successful experiment

## Prometheus Metrics

```yaml
# Agent metrics
arc_agent_request_duration_seconds{agent_type, operation}
arc_agent_request_total{agent_type, operation, status}
arc_agent_tokens_used_total{agent_type}
arc_agent_confidence_score{agent_type, decision_type}

# Experiment metrics
arc_experiment_duration_seconds{domain, status}
arc_experiment_total{domain, status}
arc_samples_created_total{experiment_id}
arc_measurements_total{property, instrument}

# Hardware metrics
arc_printer_job_duration_seconds{printer_type}
arc_printer_utilization_ratio{printer_id}
arc_furnace_cycle_duration_seconds{furnace_id}
arc_powder_consumption_kg{material}

# Safety metrics
arc_approval_requests_total{risk_class, status}
arc_safety_incidents_total{severity}
arc_guardrail_violations_total{guardrail_type}
```

## Logging Standards

### Log Format
```json
{
  "timestamp": "2025-01-15T10:30:45.123Z",
  "level": "INFO",
  "service": "planner-agent",
  "traceId": "abc123",
  "spanId": "def456",
  "experimentId": "EXP-001",
  "message": "Generated 5 candidate compositions",
  "metadata": {
    "objectiveId": "OBJ-134",
    "branchingFactor": 5,
    "tokensUsed": 1245,
    "duration_ms": 234
  }
}
```

### Log Levels
- **DEBUG**: Detailed information for debugging
- **INFO**: General informational messages
- **WARN**: Warning messages for potentially harmful situations
- **ERROR**: Error events that might still allow continued operation
- **FATAL**: Severe errors that lead to program termination

### Structured Logging Fields
```typescript
interface LogEntry {
  // Required fields
  timestamp: string;
  level: LogLevel;
  service: string;
  message: string;
  
  // Trace context
  traceId?: string;
  spanId?: string;
  parentSpanId?: string;
  
  // Business context
  experimentId?: string;
  sampleId?: string;
  agentType?: string;
  
  // Performance
  duration_ms?: number;
  tokensUsed?: number;
  
  // Error details
  error?: {
    type: string;
    message: string;
    stack?: string;
  };
  
  // Additional metadata
  metadata?: Record<string, any>;
}
```

## Tracing Configuration

### OpenTelemetry Setup
```yaml
otel:
  service_name: arc-materials-platform
  traces:
    endpoint: http://jaeger-collector:14268/api/traces
    sampling_rate: 0.1  # Sample 10% of requests
    always_sample_errors: true
  
  propagators:
    - tracecontext
    - baggage
```

### Key Spans
```typescript
// Example span attributes
const span = tracer.startSpan('experiment.execute', {
  attributes: {
    'experiment.id': experimentId,
    'experiment.domain': 'magnetics',
    'experiment.objective': objectiveId,
    'agent.type': 'planner',
    'agent.id': agentId
  }
});
```

## Grafana Dashboards

### System Overview Dashboard
```json
{
  "dashboard": {
    "title": "ARC Platform Overview",
    "panels": [
      {
        "title": "Active Experiments",
        "query": "sum(arc_experiment_total{status='executing'})"
      },
      {
        "title": "Success Rate (7d)",
        "query": "sum(rate(arc_experiment_total{status='completed'}[7d])) / sum(rate(arc_experiment_total[7d]))"
      },
      {
        "title": "Agent Response Time",
        "query": "histogram_quantile(0.95, arc_agent_request_duration_seconds)"
      },
      {
        "title": "Printer Utilization",
        "query": "avg(arc_printer_utilization_ratio)"
      }
    ]
  }
}
```

### Agent Performance Dashboard
- Request rates by agent type
- Token usage trends
- Error rates and types
- Confidence score distributions
- Decision success rates

### Hardware Utilization Dashboard
- Printer job queue depth
- Print success rates
- Furnace cycle times
- Powder consumption rates
- Equipment availability

## Alerting Rules

```yaml
groups:
  - name: agent_alerts
    rules:
      - alert: HighAgentLatency
        expr: histogram_quantile(0.95, arc_agent_request_duration_seconds) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Agent {{ $labels.agent_type }} has high latency"
          description: "95th percentile latency is {{ $value }}s"
      
      - alert: AgentDown
        expr: up{job="arc-agent"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Agent {{ $labels.instance }} is down"
  
  - name: experiment_alerts
    rules:
      - alert: ExperimentStalled
        expr: |
          changes(arc_experiment_total{status="executing"}[1h]) == 0
          and arc_experiment_total{status="executing"} > 0
        for: 2h
        labels:
          severity: warning
        annotations:
          summary: "Experiment {{ $labels.experiment_id }} has stalled"
      
      - alert: HighFailureRate
        expr: |
          sum(rate(arc_experiment_total{status="failed"}[1h])) 
          / sum(rate(arc_experiment_total[1h])) > 0.2
        for: 30m
        labels:
          severity: critical
        annotations:
          summary: "High experiment failure rate: {{ $value | humanizePercentage }}"
  
  - name: hardware_alerts
    rules:
      - alert: PrinterOffline
        expr: arc_printer_heartbeat_timestamp < time() - 300
        labels:
          severity: critical
        annotations:
          summary: "Printer {{ $labels.printer_id }} is offline"
      
      - alert: LowPowderInventory
        expr: arc_powder_remaining_kg < 5
        labels:
          severity: warning
        annotations:
          summary: "Low powder inventory for {{ $labels.material }}: {{ $value }}kg"
  
  - name: safety_alerts
    rules:
      - alert: SafetyIncident
        expr: increase(arc_safety_incidents_total[1m]) > 0
        labels:
          severity: critical
        annotations:
          summary: "Safety incident reported: {{ $labels.severity }}"
      
      - alert: CalibrationExpired
        expr: arc_calibration_valid_until_timestamp < time()
        labels:
          severity: warning
        annotations:
          summary: "Calibration expired for {{ $labels.instrument_id }}"
```

## SLOs (Service Level Objectives)

```yaml
slos:
  - name: api_availability
    target: 99.9%
    indicator:
      ratio:
        good: http_requests_total{status!~"5.."}
        total: http_requests_total
    window: 30d
  
  - name: experiment_success_rate
    target: 80%
    indicator:
      ratio:
        good: arc_experiment_total{status="completed"}
        total: arc_experiment_total{status=~"completed|failed"}
    window: 7d
  
  - name: agent_latency
    target: 95%
    indicator:
      threshold:
        metric: arc_agent_request_duration_seconds
        threshold: 5s
        percentile: 0.95
    window: 1d
```

## Log Aggregation

### Elasticsearch Mapping
```json
{
  "mappings": {
    "properties": {
      "timestamp": { "type": "date" },
      "level": { "type": "keyword" },
      "service": { "type": "keyword" },
      "message": { "type": "text" },
      "traceId": { "type": "keyword" },
      "spanId": { "type": "keyword" },
      "experimentId": { "type": "keyword" },
      "metadata": { "type": "object", "enabled": false }
    }
  }
}
```

### Common Queries
```json
// Find all errors for an experiment
{
  "query": {
    "bool": {
      "must": [
        { "term": { "experimentId": "EXP-001" } },
        { "term": { "level": "ERROR" } }
      ]
    }
  },
  "sort": [{ "timestamp": "desc" }]
}

// Agent performance analysis
{
  "aggs": {
    "agents": {
      "terms": { "field": "service" },
      "aggs": {
        "avg_duration": {
          "avg": { "field": "metadata.duration_ms" }
        }
      }
    }
  }
}
```

## Distributed Tracing

### Trace Context Propagation
```typescript
// Example middleware
function traceMiddleware(req: Request, res: Response, next: Function) {
  const span = tracer.startSpan(req.path, {
    attributes: {
      'http.method': req.method,
      'http.url': req.url,
      'user.id': req.user?.id
    }
  });
  
  req.span = span;
  res.on('finish', () => {
    span.setAttributes({
      'http.status_code': res.statusCode
    });
    span.end();
  });
  
  next();
}
```

## Health Checks

### Liveness Probe
```typescript
app.get('/health/live', (req, res) => {
  res.status(200).json({ status: 'alive' });
});
```

### Readiness Probe
```typescript
app.get('/health/ready', async (req, res) => {
  const checks = await Promise.all([
    checkDatabase(),
    checkMessageQueue(),
    checkExternalServices()
  ]);
  
  const allHealthy = checks.every(c => c.healthy);
  res.status(allHealthy ? 200 : 503).json({
    status: allHealthy ? 'ready' : 'not ready',
    checks: checks
  });
});
```

## Performance Profiling

### Continuous Profiling with Pyroscope
```yaml
pyroscope:
  enabled: true
  server_address: http://pyroscope:4040
  application_name: arc-materials
  tags:
    environment: production
    service: ${SERVICE_NAME}
```

## Cost Monitoring

```sql
-- Daily cost rollup
CREATE MATERIALIZED VIEW daily_costs AS
SELECT 
  date_trunc('day', created_at) as day,
  SUM(tokens_used * 0.00002) as llm_cost,
  COUNT(*) * 0.50 as experiment_cost,
  SUM(EXTRACT(epoch FROM completed_at - started_at) / 3600 * 2.50) as compute_cost
FROM experiments
WHERE status = 'completed'
GROUP BY 1;
```
