# Authentication & Authorization

## Service Authentication
- Inter-service communication uses mTLS
- Each agent has a unique service account with scoped permissions
- JWT tokens for API access with 1-hour expiration

## JWT Token Structure
```json
{
  "sub": "user-123",
  "email": "<EMAIL>",
  "roles": ["researcher", "process_engineer"],
  "exp": **********,
  "iat": **********,
  "iss": "arc-materials.io",
  "aud": "arc-api"
}
```

## Role-Based Access Control

```yaml
roles:
  researcher:
    permissions:
      - objectives:create
      - objectives:read
      - experiments:read
      - measurements:read
      - samples:read
      
  process_engineer:
    permissions:
      - all_of: researcher
      - parameters:modify
      - print_jobs:create
      - print_jobs:modify
      - approvals:r2
      
  lab_manager:
    permissions:
      - all_of: process_engineer
      - approvals:r3
      - safety:override
      - inventory:manage
      
  system_admin:
    permissions:
      - "*"
```

## API Key Management
```typescript
interface APIKey {
  id: string;
  name: string;
  permissions: string[];
  rateLimit: {
    requestsPerHour: number;
    tokensPerDay: number;
  };
  expiresAt?: Date;
  lastUsed?: Date;
  createdBy: string;
}
```

## Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Auth Service
    participant Resource API
    
    Client->>Auth Service: POST /auth/login
    Auth Service-->>Client: JWT Token
    
    Client->>API Gateway: GET /api/resource + Bearer Token
    API Gateway->>API Gateway: Validate JWT
    API Gateway->>Resource API: Forward request + User context
    Resource API-->>API Gateway: Response
    API Gateway-->>Client: Response
```

## Service-to-Service Authentication

### mTLS Configuration
```yaml
tls:
  mode: STRICT
  certificates:
    - cert: /certs/service.crt
      key: /certs/service.key
      ca: /certs/ca.crt
  client_auth: REQUIRE_AND_VERIFY_CLIENT_CERT
```

### Service Account Example
```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: design-agent
  namespace: arc-materials
  annotations:
    arc.io/permissions: "experiments:read,samples:create,measurements:create"
---
apiVersion: v1
kind: Secret
metadata:
  name: design-agent-token
  namespace: arc-materials
  annotations:
    kubernetes.io/service-account.name: design-agent
type: kubernetes.io/service-account-token
```

## OAuth2 Integration (External Users)

```yaml
oauth2:
  providers:
    - name: corporate_sso
      type: oidc
      issuer: https://sso.company.com
      client_id: ${OAUTH_CLIENT_ID}
      client_secret: ${OAUTH_CLIENT_SECRET}
      scopes:
        - openid
        - profile
        - email
      role_mapping:
        admin: system_admin
        engineer: process_engineer
        scientist: researcher
```

## Security Headers

All API responses include:
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Content-Security-Policy: default-src 'self'
Strict-Transport-Security: max-age=********; includeSubDomains
```

## Rate Limiting

```typescript
interface RateLimitConfig {
  anonymous: {
    requests_per_minute: 10;
    requests_per_hour: 100;
  };
  authenticated: {
    requests_per_minute: 60;
    requests_per_hour: 1000;
  };
  api_key: {
    // Configured per key
  };
}
```

## Audit Logging

All authentication events are logged:
```json
{
  "timestamp": "2025-01-15T10:30:45.123Z",
  "event": "authentication",
  "user": "<EMAIL>",
  "method": "jwt",
  "ip": "*************",
  "user_agent": "ARC-Client/1.0",
  "result": "success",
  "mfa": true
}
```

## Multi-Factor Authentication

### TOTP Configuration
```typescript
interface MFAConfig {
  required_for_roles: ['lab_manager', 'system_admin'];
  enforcement: 'strict' | 'optional';
  backup_codes: number;
  remember_device_days: 30;
}
```

## Session Management

```typescript
interface SessionConfig {
  jwt_expiry: '1h';
  refresh_token_expiry: '7d';
  idle_timeout: '30m';
  concurrent_sessions: 3;
  session_store: 'redis';
}
```

## Security Best Practices

1. **Principle of Least Privilege**: Users and services only get permissions they need
2. **Defense in Depth**: Multiple layers of security (network, application, data)
3. **Zero Trust**: Verify everything, trust nothing
4. **Encryption**: All data encrypted in transit and at rest
5. **Regular Audits**: Automated security scanning and manual reviews
6. **Incident Response**: Clear procedures for security incidents

## Compliance

- SOC 2 Type II compliant
- GDPR compliant for EU data
- ITAR compliant for restricted materials
- Regular penetration testing
- Annual security audits
