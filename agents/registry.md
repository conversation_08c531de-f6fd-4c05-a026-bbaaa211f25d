# Agent Registry Specification

## Registry Schema

```typescript
interface AgentRegistration {
  id: string;
  type: AgentType;
  capabilities: Capability[];
  schemas: {
    input: JSONSchema;
    output: J<PERSON>NSchema;
  };
  sla: {
    maxLatencyMs: number;
    availability: number;
  };
  safetyClass: 'S1' | 'S2' | 'S3' | 'R1' | 'R2' | 'R3';
  requiredResources: Resource[];
}

interface Capability {
  name: string;
  version: string;
  constraints: Constraint[];
}
```

## Agent Types

### Planner Agent
- Converts objectives into executable task graphs
- Supports single-path and multi-path planning modes
- Implements iterative re-planning based on feedback

### Design Agent
- Proposes material compositions and process parameters
- Uses LLM-guided modification loop with self-reflection
- Maintains modification history for convergence optimization

### Simulation Agent
- Interfaces with Desktop Metal Live Sinter
- Computes distortion compensation and temperature profiles
- Updates acceptance criteria based on simulation results

### Printer Cell Agents
- One agent per printer type (Shop, P-1, X25 Pro, X160 Pro)
- Manages job submission via Live Suite API
- Collects telemetry and monitors print status

### Post-Process Agent
- Controls sintering furnaces and depowdering stations
- Implements parameter search with safety guardrails
- Requires HITL approval for new schedules (R3+ risk class)

### Metrology Agent
- Manages measurement pipelines for various properties
- Handles calibration and uncertainty quantification
- Outputs structured measurement records

### Critic Agent
- Implements self/cross/human reflection patterns
- Validates constraints and physical plausibility
- Triggers escalation to HITL when needed

## Registration Process

Agents register themselves on startup:

```typescript
POST /registry/agents
{
  "id": "design-agent-001",
  "type": "design",
  "capabilities": [
    {
      "name": "composition-generation",
      "version": "1.0",
      "constraints": [
        {
          "type": "max-elements",
          "value": 10
        }
      ]
    }
  ],
  "schemas": {
    "input": { /* JSON Schema */ },
    "output": { /* JSON Schema */ }
  },
  "sla": {
    "maxLatencyMs": 5000,
    "availability": 0.99
  },
  "safetyClass": "S2"
}
```

## Health Monitoring

Agents must implement health endpoints:

```typescript
GET /health
{
  "status": "healthy",
  "version": "1.2.3",
  "uptime": 3600,
  "metrics": {
    "requestsPerSecond": 10.5,
    "averageLatencyMs": 250,
    "errorRate": 0.001
  }
}
```
