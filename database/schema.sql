-- ARC Materials Discovery Platform Database Schema
-- PostgreSQL 14+

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schema
CREATE SCHEMA IF NOT EXISTS arc;
SET search_path TO arc, public;

-- <PERSON><PERSON> types
CREATE TYPE domain_type AS ENUM ('magnetics', 'electrolytes', 'ferromagnets', 'custom');
CREATE TYPE goal_operator AS ENUM ('>=', '<=', '==', 'approx');
CREATE TYPE risk_class AS ENUM ('R1', 'R2', 'R3');
CREATE TYPE safety_class AS ENUM ('S1', 'S2', 'S3');
CREATE TYPE printer_type AS ENUM ('Shop', 'P1', 'X25Pro', 'X160Pro');
CREATE TYPE experiment_status AS ENUM ('created', 'planning', 'executing', 'completed', 'failed', 'cancelled');
CREATE TYPE agent_type AS ENUM ('planner', 'design', 'simulation', 'printer', 'postprocess', 'metrology', 'critic', 'datasteward');
CREATE TYPE decision_type AS ENUM ('composition_selection', 'parameter_optimization', 'acceptance_check', 'replan');

-- Core experiment tracking
CREATE TABLE objectives (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    domain domain_type NOT NULL,
    target_metrics JSONB NOT NULL,
    constraints JSONB,
    risk_class risk_class DEFAULT 'R1',
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE experiments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    objective_id UUID NOT NULL REFERENCES objectives(id),
    status experiment_status NOT NULL DEFAULT 'created',
    plan_id UUID,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE samples (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    experiment_id UUID REFERENCES experiments(id),
    design_id UUID NOT NULL,
    composition JSONB NOT NULL,
    process_parameters JSONB NOT NULL,
    expected_properties JSONB,
    actual_properties JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE measurements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sample_id UUID REFERENCES samples(id),
    property VARCHAR(100) NOT NULL,
    value DOUBLE PRECISION NOT NULL,
    unit VARCHAR(50) NOT NULL,
    uncertainty JSONB,
    conditions JSONB,
    calibration_id UUID,
    instrument_id VARCHAR(100),
    operator VARCHAR(100),
    measured_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    validated BOOLEAN DEFAULT false,
    validation_notes TEXT
);

-- Manufacturing tracking
CREATE TABLE print_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sample_id UUID REFERENCES samples(id),
    printer printer_type NOT NULL,
    job_id_external VARCHAR(200),
    build_file_url TEXT,
    status VARCHAR(50) NOT NULL,
    powder_batch VARCHAR(100),
    layer_thickness_um INTEGER,
    binder_type VARCHAR(50),
    binder_saturation_pct NUMERIC(5,2),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    telemetry_url TEXT,
    parameters JSONB,
    alerts JSONB
);

CREATE TABLE sinter_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sample_id UUID REFERENCES samples(id),
    furnace_id VARCHAR(100),
    schedule JSONB NOT NULL,
    actual_profile JSONB,
    atmosphere VARCHAR(50),
    status VARCHAR(50) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    alerts JSONB
);

-- Agent activity tracking
CREATE TABLE agent_decisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    experiment_id UUID REFERENCES experiments(id),
    agent_type agent_type NOT NULL,
    decision_type decision_type NOT NULL,
    inputs JSONB NOT NULL,
    outputs JSONB NOT NULL,
    confidence DOUBLE PRECISION,
    reflection TEXT,
    tokens_used INTEGER,
    latency_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE agent_registry (
    id VARCHAR(100) PRIMARY KEY,
    agent_type agent_type NOT NULL,
    capabilities JSONB NOT NULL,
    schemas JSONB NOT NULL,
    sla JSONB,
    safety_class safety_class DEFAULT 'S1',
    status VARCHAR(50) DEFAULT 'offline',
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Safety and approvals
CREATE TABLE approval_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_type VARCHAR(100) NOT NULL,
    risk_class risk_class NOT NULL,
    requester VARCHAR(100) NOT NULL,
    request_data JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE approvals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID REFERENCES approval_requests(id),
    approver VARCHAR(100) NOT NULL,
    decision VARCHAR(50) NOT NULL,
    comment TEXT,
    approved_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE safety_incidents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    experiment_id UUID REFERENCES experiments(id),
    severity VARCHAR(50) NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    actions_taken JSONB,
    reported_by VARCHAR(100),
    reported_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE,
    investigation_notes TEXT
);

-- Calibration tracking
CREATE TABLE calibrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instrument_id VARCHAR(100) NOT NULL,
    calibration_type VARCHAR(100) NOT NULL,
    standard_used VARCHAR(200),
    performed_by VARCHAR(100),
    certificate_number VARCHAR(100),
    results JSONB,
    valid_until DATE NOT NULL,
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Material inventory
CREATE TABLE powder_inventory (
    batch_id VARCHAR(100) PRIMARY KEY,
    material VARCHAR(100) NOT NULL,
    supplier VARCHAR(200),
    lot_number VARCHAR(100),
    quantity_kg NUMERIC(10,3),
    remaining_kg NUMERIC(10,3),
    particle_size_d50_um NUMERIC(6,2),
    certificate_url TEXT,
    received_date DATE,
    expiry_date DATE,
    storage_location VARCHAR(100),
    properties JSONB
);

-- Audit trail
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_by VARCHAR(100),
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    reason TEXT
);

-- Indexes for performance
CREATE INDEX idx_experiments_objective ON experiments(objective_id);
CREATE INDEX idx_experiments_status ON experiments(status);
CREATE INDEX idx_samples_experiment ON samples(experiment_id);
CREATE INDEX idx_measurements_sample ON measurements(sample_id);
CREATE INDEX idx_measurements_property ON measurements(property);
CREATE INDEX idx_measurements_timestamp ON measurements(measured_at);
CREATE INDEX idx_print_jobs_sample ON print_jobs(sample_id);
CREATE INDEX idx_print_jobs_status ON print_jobs(status);
CREATE INDEX idx_agent_decisions_experiment ON agent_decisions(experiment_id);
CREATE INDEX idx_agent_decisions_type ON agent_decisions(agent_type, decision_type);
CREATE INDEX idx_approval_requests_status ON approval_requests(status);
CREATE INDEX idx_audit_log_table_record ON audit_log(table_name, record_id);
CREATE INDEX idx_audit_log_timestamp ON audit_log(changed_at);

-- Full text search indexes
CREATE INDEX idx_agent_decisions_reflection ON agent_decisions USING gin(to_tsvector('english', reflection));
CREATE INDEX idx_safety_incidents_description ON safety_incidents USING gin(to_tsvector('english', description));

-- Functions and triggers
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_objectives_updated_at BEFORE UPDATE ON objectives
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_experiments_updated_at BEFORE UPDATE ON experiments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

-- Audit logging function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log(table_name, record_id, action, old_values, changed_by)
        VALUES (TG_TABLE_NAME, OLD.id, TG_OP, row_to_json(OLD), current_user);
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log(table_name, record_id, action, old_values, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(OLD), row_to_json(NEW), current_user);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log(table_name, record_id, action, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(NEW), current_user);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to critical tables
CREATE TRIGGER audit_samples AFTER INSERT OR UPDATE OR DELETE ON samples
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_measurements AFTER INSERT OR UPDATE OR DELETE ON measurements
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_approvals AFTER INSERT OR UPDATE OR DELETE ON approvals
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Views for common queries
CREATE VIEW v_experiment_summary AS
SELECT 
    e.id,
    e.status,
    o.domain,
    o.target_metrics,
    COUNT(DISTINCT s.id) as sample_count,
    COUNT(DISTINCT m.id) as measurement_count,
    AVG(ad.confidence) as avg_confidence,
    e.created_at,
    e.updated_at
FROM experiments e
JOIN objectives o ON e.objective_id = o.id
LEFT JOIN samples s ON s.experiment_id = e.id
LEFT JOIN measurements m ON m.sample_id = s.id
LEFT JOIN agent_decisions ad ON ad.experiment_id = e.id
GROUP BY e.id, e.status, o.domain, o.target_metrics, e.created_at, e.updated_at;

CREATE VIEW v_sample_performance AS
SELECT 
    s.id as sample_id,
    s.composition,
    s.process_parameters,
    m.property,
    m.value,
    m.unit,
    m.uncertainty,
    m.measured_at
FROM samples s
JOIN measurements m ON m.sample_id = s.id
WHERE m.validated = true;

-- Permissions (example for different roles)
GRANT SELECT ON ALL TABLES IN SCHEMA arc TO readonly_user;
GRANT SELECT, INSERT, UPDATE ON objectives, experiments, samples, measurements TO researcher;
GRANT ALL ON ALL TABLES IN SCHEMA arc TO admin_user;

-- Comments for documentation
COMMENT ON TABLE objectives IS 'High-level research objectives with target metrics';
COMMENT ON TABLE experiments IS 'Individual experiments pursuing an objective';
COMMENT ON TABLE samples IS 'Physical samples created during experiments';
COMMENT ON TABLE measurements IS 'Property measurements taken on samples';
COMMENT ON TABLE agent_decisions IS 'Decision history from AI agents';
COMMENT ON COLUMN measurements.uncertainty IS 'JSON object with type, value, and confidence level';
COMMENT ON COLUMN samples.composition IS 'JSON object mapping element symbols to mass fractions';
