# ARC Materials Discovery Platform - System Architecture

## Overview

This system implements a graph-orchestrated, multi-agent platform for autonomous materials discovery integrated with Desktop Metal's additive manufacturing systems. The architecture follows agentic design patterns with planning, tool-use, reflection, and multi-agent collaboration.

## System Architecture Diagram

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI[Web Dashboard]
        API[REST API Gateway]
    end
    
    subgraph "Orchestration Layer"
        GO[Graph Orchestrator]
        EM[Event Manager]
        TS[Trace Store]
    end
    
    subgraph "Agent Layer"
        PA[Planner Agent]
        DA[Design Agent]
        SA[Simulation Agent]
        PCA[Printer Cell Agents]
        PPA[Post-Process Agent]
        MA[Metrology Agent]
        CA[Critic Agent]
        DSA[Data Steward Agent]
    end
    
    subgraph "Integration Layer"
        LS[Live Suite Connector]
        LSim[Live Sinter Interface]
        ROS[ROS Interface]
    end
    
    subgraph "Data Layer"
        EDB[(Experiment DB)]
        VDB[(Vector Store)]
        KG[(Knowledge Graph)]
        TS2[(Time Series DB)]
    end
    
    subgraph "Hardware Layer"
        DM[Desktop Metal Printers]
        FUR[Furnaces]
        ROB[Robotics]
        MET[Metrology Equipment]
    end
    
    UI --> API
    API --> GO
    GO --> EM
    GO --> TS
    EM --> PA
    EM --> DA
    EM --> SA
    EM --> PCA
    EM --> PPA
    EM --> MA
    EM --> CA
    EM --> DSA
    
    PA --> EDB
    DA --> VDB
    SA --> LSim
    PCA --> LS
    
    LS --> DM
    ROS --> ROB
    PPA --> FUR
    MA --> MET
```

## Key Design Principles

1. **Event-Driven Architecture**: All agent communication happens through an event bus for loose coupling
2. **Graph-Based Orchestration**: Workflows are represented as DAGs with checkpointing and recovery
3. **Typed Interfaces**: All agent interactions use strongly-typed JSON schemas
4. **Safety First**: Multiple layers of validation, guardrails, and HITL approval gates
5. **Observable by Design**: Comprehensive tracing, metrics, and logging throughout
